/**
 * 认证混入
 * 为页面提供统一的登录检查和用户状态管理
 */

import { AuthHelper } from '@/utils/auth-helper.js';
import { ToastManager } from '@/utils/toast-manager.js';

export const authMixin = {
  data() {
    return {
      userInfo: null,
      authLoading: true,
      requireAuth: false, // 子组件可以设置为true来要求登录
      requireVip: false, // 子组件可以设置VIP等级要求
      vipLevel: 'v1' // 默认VIP等级要求
    };
  },

  async onLoad(options) {
    // 如果页面需要登录，进行检查
    if (this.requireAuth) {
      await this.checkAuthStatus();
    }
    
    // 调用子组件的onLoad方法
    if (this.$options.methods && this.$options.methods.onLoadAfterAuth) {
      await this.onLoadAfterAuth(options);
    }
  },

  onShow() {
    // 如果页面需要登录，每次显示时都检查
    if (this.requireAuth) {
      this.checkAuthOnShow();
    }
    
    // 调用子组件的onShow方法
    if (this.$options.methods && this.$options.methods.onShowAfterAuth) {
      this.onShowAfterAuth();
    }
  },

  methods: {
    // 检查登录状态
    async checkAuthStatus() {
      try {
        this.authLoading = true;
        
        if (!AuthHelper.isLoggedIn()) {
          this.redirectToLogin();
          return false;
        }

        // 加载用户信息
        await this.loadUserInfo();

        // 检查VIP权限
        if (this.requireVip) {
          if (!this.checkVipAccess()) {
            return false;
          }
        }

        return true;
      } catch (error) {
        console.error('检查登录状态失败:', error);
        this.redirectToLogin();
        return false;
      } finally {
        this.authLoading = false;
      }
    },

    // 页面显示时的登录检查（简化版）
    checkAuthOnShow() {
      if (!AuthHelper.isLoggedIn()) {
        this.redirectToLogin();
        return false;
      }

      // 检查VIP权限
      if (this.requireVip) {
        if (!this.checkVipAccess()) {
          return false;
        }
      }

      // 刷新用户信息（不阻塞）
      this.loadUserInfo().catch(error => {
        console.error('刷新用户信息失败:', error);
      });

      return true;
    },

    // 加载用户信息
    async loadUserInfo() {
      try {
        const userInfo = AuthHelper.getCurrentUser();
        
        if (userInfo) {
          this.userInfo = userInfo;
        } else {
          // 如果本地没有用户信息，尝试从服务器获取
          const result = await AuthHelper.refreshUserInfo();
          if (result.success) {
            this.userInfo = result.user;
          } else {
            throw new Error('获取用户信息失败');
          }
        }
      } catch (error) {
        console.error('加载用户信息失败:', error);
        this.redirectToLogin();
        throw error;
      }
    },

    // 检查VIP权限
    checkVipAccess() {
      if (!this.requireVip) return true;
      
      return AuthHelper.checkVipAccess(this.vipLevel);
    },

    // 跳转到登录页
    redirectToLogin() {
      ToastManager.error('请先登录');
      AuthHelper.redirectToLogin();
    },

    // 获取用户显示名称
    getUserDisplayName() {
      if (!this.userInfo) return '未登录';
      return this.userInfo.full_name || this.userInfo.username || '用户';
    },

    // 获取用户邮箱
    getUserEmail() {
      if (!this.userInfo) return '';
      return this.userInfo.email || '';
    },

    // 获取用户头像
    getUserAvatar() {
      if (!this.userInfo || !this.userInfo.avatar_url) {
        return '/static/figma-assets/assets/b0b4af09-8098-4d18-aed3-71537580e1af.png';
      }
      return this.userInfo.avatar_url;
    },

    // 获取用户VIP等级
    getUserVipLevel() {
      if (!this.userInfo) return 'none';
      return this.userInfo.vip_level || 'none';
    },

    // 检查是否为VIP用户
    isVipUser() {
      const vipLevel = this.getUserVipLevel();
      return vipLevel && vipLevel !== 'none';
    },

    // 检查VIP是否过期
    isVipExpired() {
      if (!this.userInfo || !this.userInfo.vip_expires_at) return true;
      return new Date(this.userInfo.vip_expires_at) < new Date();
    },

    // 获取VIP过期时间
    getVipExpiresAt() {
      if (!this.userInfo) return null;
      return this.userInfo.vip_expires_at;
    },

    // 格式化VIP过期时间
    formatVipExpiresAt() {
      const expiresAt = this.getVipExpiresAt();
      if (!expiresAt) return '';
      
      const date = new Date(expiresAt);
      return date.toLocaleDateString('zh-CN');
    },

    // 执行登出
    async performLogout() {
      try {
        const confirmed = await ToastManager.confirm({
          title: '提示',
          content: '确定要退出登录吗？',
          confirmText: '退出',
          cancelText: '取消'
        });

        if (confirmed) {
          const result = await AuthHelper.logout();
          if (result.success) {
            // 登出成功，跳转到登录页
            uni.reLaunch({
              url: '/pages/login/login'
            });
          }
        }
      } catch (error) {
        console.error('退出登录失败:', error);
        ToastManager.error('退出失败，请重试');
      }
    },

    // 检查并要求登录
    requireLogin() {
      if (!AuthHelper.isLoggedIn()) {
        this.redirectToLogin();
        return false;
      }
      return true;
    },

    // 检查并要求VIP权限
    requireVipAccess(level = 'v1') {
      if (!this.requireLogin()) return false;
      
      return AuthHelper.checkVipAccess(level);
    }
  }
};

export default authMixin;

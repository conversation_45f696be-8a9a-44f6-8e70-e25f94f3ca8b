import { NextRequest } from 'next/server';
import { ActivationCodeManager } from '@/lib/activation-codes';
import {
  withAdminAccess,
  successResponse,
  errorResponse,
  validateRequest,
  logAuditEvent
} from '@/lib/api-utils';

// GET /api/activation-codes/[id] - 获取单个激活码详情 (仅管理员)
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return withAdminAccess()(request, async (req, user, session, clientInfo) => {
    try {
      const id = parseInt(params.id);
      if (isNaN(id)) {
        return errorResponse('Invalid activation code ID', 400);
      }

      const code = await ActivationCodeManager.getActivationCodeById(id);

      await logAuditEvent({
        userId: user.id,
        action: 'ACTIVATION_CODE_VIEWED',
        resource: 'activation_codes',
        resourceId: id.toString(),
      });

      return successResponse(code, 'Activation code retrieved successfully');
    } catch (error) {
      if (error instanceof Error && error.message === 'Activation code not found') {
        return errorResponse('Activation code not found', 404);
      }
      console.error('Get activation code error:', error);
      return errorResponse('Failed to retrieve activation code', 500);
    }
  });
}

// PATCH /api/activation-codes/[id] - 更新激活码状态 (仅管理员)
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return withAdminAccess()(request, async (req, user, session, clientInfo) => {
    try {
      const id = parseInt(params.id);
      if (isNaN(id)) {
        return errorResponse('Invalid activation code ID', 400);
      }

      const body = await request.json();

      // 验证请求数据
      const { isValid, errors, data } = validateRequest<{
        status: 'active' | 'disabled';
        description?: string;
      }>(body, {
        status: (value) => {
          const validStatuses = ['active', 'disabled'];
          if (!validStatuses.includes(value)) {
            return 'Invalid status. Only active and disabled are allowed';
          }
          return null;
        },
      });

      if (!isValid) {
        return errorResponse('Validation failed', 400, 'VALIDATION_ERROR', errors);
      }

      const { status, description } = data;

      // 检查激活码是否存在
      const existingCode = await ActivationCodeManager.getActivationCodeById(id);
      
      if (existingCode.status === 'used') {
        return errorResponse('Cannot modify used activation code', 400);
      }

      // 更新状态
      await ActivationCodeManager.updateActivationCodeStatus(id, status, user.id);

      // 如果有描述，也更新描述
      if (description !== undefined) {
        await ActivationCodeManager.updateDescription(id, description);
      }

      await logAuditEvent({
        userId: user.id,
        action: 'ACTIVATION_CODE_UPDATED',
        resource: 'activation_codes',
        resourceId: id.toString(),
        details: {
          old_status: existingCode.status,
          new_status: status,
          description
        },
      });

      return successResponse(null, 'Activation code updated successfully');
    } catch (error) {
      if (error instanceof Error && error.message === 'Activation code not found') {
        return errorResponse('Activation code not found', 404);
      }
      console.error('Update activation code error:', error);
      return errorResponse('Failed to update activation code', 500);
    }
  });
}

// DELETE /api/activation-codes/[id] - 删除激活码 (仅管理员)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return withAdminAccess()(request, async (req, user, session, clientInfo) => {
    try {
      const id = parseInt(params.id);
      if (isNaN(id)) {
        return errorResponse('Invalid activation code ID', 400);
      }

      // 检查激活码是否存在
      const existingCode = await ActivationCodeManager.getActivationCodeById(id);

      // 删除激活码
      await ActivationCodeManager.deleteActivationCode(id, user.id);

      await logAuditEvent({
        userId: user.id,
        action: 'ACTIVATION_CODE_DELETED',
        resource: 'activation_codes',
        resourceId: id.toString(),
        details: {
          code: existingCode.code,
          vip_level: existingCode.vip_level,
          status: existingCode.status
        },
      });

      return successResponse(null, 'Activation code deleted successfully');
    } catch (error) {
      if (error instanceof Error && error.message === 'Activation code not found') {
        return errorResponse('Activation code not found', 404);
      }
      if (error instanceof Error && error.message === 'Cannot delete used activation code') {
        return errorResponse('Cannot delete used activation code', 400);
      }
      console.error('Delete activation code error:', error);
      return errorResponse('Failed to delete activation code', 500);
    }
  });
}

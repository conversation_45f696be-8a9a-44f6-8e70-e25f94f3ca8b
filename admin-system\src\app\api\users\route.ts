import { NextRequest } from 'next/server';
import { UserManager } from '@/lib/auth';
import {
  withAuth,
  withRole,
  withAdminAccess,
  successResponse,
  errorResponse,
  getPaginationParams,
  logAuditEvent
} from '@/lib/api-utils';

// GET /api/users - Get all users with filters (admin only, admin web client only)
export async function GET(request: NextRequest) {
  return withAdminAccess()(request, async (req, user, session, clientInfo) => {
    try {
      const url = new URL(request.url);
      const searchParams = url.searchParams;

      // Parse pagination parameters
      const page = parseInt(searchParams.get('page') || '1');
      const limit = parseInt(searchParams.get('limit') || '20');

      // Parse filter parameters
      const search = searchParams.get('search') || undefined;
      const role = searchParams.get('role') || undefined;
      const vip_level = searchParams.get('vip_level') || undefined;
      const is_active = searchParams.get('is_active') ? searchParams.get('is_active') === 'true' : undefined;
      const is_banned = searchParams.get('is_banned') ? searchParams.get('is_banned') === 'true' : undefined;
      const registration_source = searchParams.get('registration_source') || undefined;
      const sort_by = searchParams.get('sort_by') || 'created_at';
      const sort_order = (searchParams.get('sort_order') || 'desc') as 'asc' | 'desc';

      const { users, total } = await UserManager.getUsersWithFilters({
        page,
        limit,
        search,
        role,
        vip_level,
        is_active,
        is_banned,
        registration_source,
        sort_by,
        sort_order,
      });

      const totalPages = Math.ceil(total / limit);

      await logAuditEvent({
        userId: user.id,
        action: 'USERS_VIEWED',
        resource: 'users',
        details: {
          page,
          limit,
          total,
          filters: { search, role, vip_level, is_active, is_banned, registration_source }
        },
      });

      return successResponse(
        users,
        'Users retrieved successfully',
        {
          page,
          limit,
          total,
          totalPages,
        }
      );
    } catch (error) {
      console.error('Get users error:', error);
      return errorResponse('获取用户列表失败', 500);
    }
  });
}

// POST /api/users - Create new user (admin only, admin web client only)
export async function POST(request: NextRequest) {
  return withAdminAccess()(request, async (req, user, session, clientInfo) => {
    try {
      const body = await request.json();
      
      // Validate required fields
      const { username, email, password, full_name, role } = body;
      
      if (!username || !email || !password) {
        return errorResponse('用户名、邮箱和密码为必填项', 400);
      }

      // Check if user already exists
      const existingUser = await UserManager.getUserByEmail(email);
      if (existingUser) {
        return errorResponse('该邮箱已被使用', 409);
      }

      const existingUsername = await UserManager.getUserByUsername(username);
      if (existingUsername) {
        return errorResponse('用户名已被占用', 409);
      }

      // Create user
      const newUser = await UserManager.createUser({
        username,
        email,
        password,
        full_name,
        role: role || 'user',
      });

      await logAuditEvent({
        userId: user.id,
        action: 'USER_CREATED',
        resource: 'users',
        resourceId: newUser.id.toString(),
        details: {
          createdUser: {
            id: newUser.id,
            username: newUser.username,
            email: newUser.email,
            role: newUser.role,
          },
        },
      });

      return successResponse(
        {
          id: newUser.id,
          username: newUser.username,
          email: newUser.email,
          full_name: newUser.full_name,
          role: newUser.role,
          avatar_url: newUser.avatar_url,
          is_active: newUser.is_active,
          created_at: newUser.created_at,
        },
        '用户创建成功'
      );
    } catch (error) {
      console.error('Create user error:', error);
      return errorResponse('创建用户失败', 500);
    }
  });
}

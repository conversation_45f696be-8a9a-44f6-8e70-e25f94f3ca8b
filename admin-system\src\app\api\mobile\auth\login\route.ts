/**
 * 移动端登录API
 * 专门为UniApp等移动客户端提供的登录接口
 */

import { NextRequest } from 'next/server';
import { User<PERSON>anager, SessionManager, JWTUtils } from '@/lib/auth';
import { 
  successResponse, 
  errorResponse, 
  validateRequest, 
  validators,
  logAuditEvent,
  getClientIP,
  rateLimit
} from '@/lib/api-utils';
import { ClientDetector, ClientType, ClientPermissionChecker } from '@/lib/client-detection';

export async function POST(request: NextRequest) {
  try {
    // 检测客户端类型
    const clientInfo = ClientDetector.getClientInfo(request);
    
    // 只允许移动端客户端访问
    if (!ClientDetector.isMobileClient(clientInfo.type)) {
      return errorResponse('此接口仅供移动端客户端使用', 403, 'INVALID_CLIENT');
    }

    // 获取客户端限流配置
    const rateLimitConfig = ClientPermissionChecker.getRateLimit(clientInfo.type);
    const clientIP = getClientIP(request);
    
    if (!rateLimit(`mobile-login:${clientIP}`, rateLimitConfig.requests, rateLimitConfig.window)) {
      return errorResponse('登录尝试次数过多，请稍后再试', 429);
    }

    const body = await request.json();

    // 验证请求参数
    const { isValid, errors, data } = validateRequest<{
      emailOrUsername: string;
      password: string;
      deviceId?: string;
      deviceName?: string;
    }>(body, {
      emailOrUsername: validators.required,
      password: validators.required,
    });

    if (!isValid) {
      return errorResponse('验证失败', 400, 'VALIDATION_ERROR');
    }

    // 认证用户
    const user = await UserManager.authenticateUser(
      data.emailOrUsername,
      data.password
    );

    if (!user) {
      // 记录失败的登录尝试
      await logAuditEvent({
        action: 'MOBILE_LOGIN_FAILED',
        details: {
          emailOrUsername: data.emailOrUsername,
          reason: 'Invalid credentials',
          clientType: clientInfo.type,
          clientVersion: clientInfo.version,
          deviceId: data.deviceId,
          deviceName: data.deviceName,
        },
        ipAddress: clientIP,
        userAgent: request.headers.get('user-agent') || undefined,
      });

      return errorResponse('用户名或密码错误', 401, 'INVALID_CREDENTIALS');
    }

    // 创建会话
    const sessionId = await SessionManager.createSession(user.id);

    // 生成JWT token（移动端token有效期更长）
    const token = JWTUtils.sign({
      userId: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      sessionId,
    });

    // 记录成功登录
    await logAuditEvent({
      userId: user.id,
      action: 'MOBILE_LOGIN_SUCCESS',
      details: {
        sessionId,
        clientType: clientInfo.type,
        clientVersion: clientInfo.version,
        deviceId: data.deviceId,
        deviceName: data.deviceName,
        platform: clientInfo.platform,
      },
      ipAddress: clientIP,
      userAgent: request.headers.get('user-agent') || undefined,
    });

    // 返回移动端专用的响应格式
    return successResponse({
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        full_name: user.full_name,
        avatar_url: user.avatar_url,
        // 移动端不返回敏感信息
      },
      token,
      expires_in: 30 * 24 * 60 * 60, // 30天（秒）
      session_id: sessionId,
      client_config: {
        // 移动端专用配置
        upload_max_size: 10 * 1024 * 1024, // 10MB
        supported_formats: ['jpg', 'png', 'gif'],
        api_version: '1.0',
      }
    }, '登录成功');

  } catch (error) {
    console.error('Mobile login error:', error);
    return errorResponse('服务器内部错误', 500);
  }
}

// 移动端专用的登出接口
export async function DELETE(request: NextRequest) {
  try {
    const clientInfo = ClientDetector.getClientInfo(request);
    
    // 只允许移动端客户端访问
    if (!ClientDetector.isMobileClient(clientInfo.type)) {
      return errorResponse('此接口仅供移动端客户端使用', 403, 'INVALID_CLIENT');
    }

    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return errorResponse('缺少授权头', 401);
    }

    const token = authHeader.substring(7);
    const payload = JWTUtils.verify(token);
    
    if (!payload) {
      return errorResponse('无效的令牌', 401);
    }

    // 删除会话
    if (payload.sessionId) {
      await SessionManager.deleteSession(payload.sessionId);
    }

    // 记录登出
    await logAuditEvent({
      userId: payload.userId,
      action: 'MOBILE_LOGOUT',
      details: {
        sessionId: payload.sessionId,
        clientType: clientInfo.type,
      },
      ipAddress: getClientIP(request),
      userAgent: request.headers.get('user-agent') || undefined,
    });

    return successResponse(null, '登出成功');

  } catch (error) {
    console.error('Mobile logout error:', error);
    return errorResponse('服务器内部错误', 500);
  }
}

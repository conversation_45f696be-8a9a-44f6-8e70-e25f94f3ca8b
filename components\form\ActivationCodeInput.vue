<template>
  <view class="activation-code-wrapper">
    <!-- 标签 -->
    <view class="form-label">
      <text>{{ label }}</text>
      <text v-if="required" class="required-mark">*</text>
    </view>

    <!-- 激活码输入容器 -->
    <view class="activation-code-container" :class="{ 'error': hasError, 'focused': isFocused }">
      <!-- 激活码输入框 -->
      <input 
        class="activation-code-input"
        type="text"
        :value="value"
        :placeholder="placeholder"
        :disabled="disabled"
        :maxlength="maxLength"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
        @confirm="handleConfirm"
      />

      <!-- 格式化显示 -->
      <view v-if="showFormatted && formattedValue" class="formatted-display">
        <text>{{ formattedValue }}</text>
      </view>

      <!-- 清除按钮 -->
      <view 
        v-if="showClear && value && !disabled" 
        class="clear-btn"
        @tap="handleClear"
      >
        <text class="clear-icon">✕</text>
      </view>
    </view>

    <!-- 错误提示 -->
    <view v-if="hasError" class="error-text">
      <text>{{ errorMessage }}</text>
    </view>

    <!-- 帮助文本 */
    <view v-if="helpText && !hasError" class="help-text">
      <text>{{ helpText }}</text>
    </view>

    <!-- 激活码格式说明 */
    <view v-if="showFormatTip" class="format-tip">
      <text>{{ formatTip }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ActivationCodeInput',
  
  props: {
    value: {
      type: String,
      default: ''
    },
    label: {
      type: String,
      default: '激活码'
    },
    placeholder: {
      type: String,
      default: '请输入激活码'
    },
    required: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    error: {
      type: String,
      default: ''
    },
    helpText: {
      type: String,
      default: '激活码用于获得VIP权限，请联系管理员获取'
    },
    showClear: {
      type: Boolean,
      default: true
    },
    showFormatted: {
      type: Boolean,
      default: false
    },
    showFormatTip: {
      type: Boolean,
      default: true
    },
    maxLength: {
      type: Number,
      default: 32
    },
    minLength: {
      type: Number,
      default: 16
    }
  },

  data() {
    return {
      isFocused: false
    };
  },

  computed: {
    // 是否有错误
    hasError() {
      return !!this.error;
    },

    // 错误信息
    errorMessage() {
      return this.error;
    },

    // 格式化后的激活码（用于显示）
    formattedValue() {
      if (!this.value) return '';
      
      // 每4位添加一个空格
      return this.value.replace(/(.{4})/g, '$1 ').trim();
    },

    // 格式提示文本
    formatTip() {
      return `激活码格式：${this.minLength}-${this.maxLength}位大写字母和数字`;
    }
  },

  methods: {
    // 处理输入
    handleInput(e) {
      let value = e.detail.value;
      
      // 转换为大写并过滤非法字符
      value = value.toUpperCase().replace(/[^A-Z0-9]/g, '');
      
      // 限制长度
      if (value.length > this.maxLength) {
        value = value.substring(0, this.maxLength);
      }
      
      this.$emit('input', value);
      this.$emit('change', value);
    },

    // 处理聚焦
    handleFocus(e) {
      this.isFocused = true;
      this.$emit('focus', e);
    },

    // 处理失焦
    handleBlur(e) {
      this.isFocused = false;
      this.$emit('blur', e);
      
      // 失焦时进行格式验证
      this.validateFormat();
    },

    // 处理确认
    handleConfirm(e) {
      this.$emit('confirm', e);
      this.validateFormat();
    },

    // 清除内容
    handleClear() {
      this.$emit('input', '');
      this.$emit('change', '');
      this.$emit('clear');
    },

    // 验证激活码格式
    validateFormat() {
      if (!this.value) return;
      
      const value = this.value.trim();
      
      // 长度验证
      if (value.length < this.minLength) {
        this.$emit('format-error', `激活码长度不能少于${this.minLength}位`);
        return false;
      }
      
      if (value.length > this.maxLength) {
        this.$emit('format-error', `激活码长度不能超过${this.maxLength}位`);
        return false;
      }
      
      // 格式验证
      if (!/^[A-Z0-9]+$/.test(value)) {
        this.$emit('format-error', '激活码只能包含大写字母和数字');
        return false;
      }
      
      this.$emit('format-valid', value);
      return true;
    },

    // 公开方法：验证激活码
    validate() {
      if (this.required && !this.value) {
        return {
          valid: false,
          message: '请输入激活码'
        };
      }
      
      if (this.value && !this.validateFormat()) {
        return {
          valid: false,
          message: '激活码格式不正确'
        };
      }
      
      return {
        valid: true,
        message: ''
      };
    }
  }
};
</script>

<style scoped>
.activation-code-wrapper {
  margin-bottom: 20px;
}

.form-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.required-mark {
  color: #ff4757;
  margin-left: 2px;
}

.activation-code-container {
  position: relative;
  display: flex;
  align-items: center;
  border: 2px solid #f0f0f0;
  border-radius: 12px;
  background: #fafafa;
  transition: all 0.3s ease;
  overflow: hidden;
}

.activation-code-container.focused {
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.activation-code-container.error {
  border-color: #ff4757;
  background: #fff5f5;
}

.activation-code-input {
  flex: 1;
  height: 50px;
  padding: 0 15px;
  font-size: 16px;
  font-family: 'Courier New', monospace;
  letter-spacing: 2px;
  border: none;
  background: transparent;
  color: #333;
  text-transform: uppercase;
}

.activation-code-input::placeholder {
  color: #ccc;
  letter-spacing: normal;
  font-family: system-ui, -apple-system, sans-serif;
}

.formatted-display {
  position: absolute;
  top: 50%;
  left: 15px;
  transform: translateY(-50%);
  font-family: 'Courier New', monospace;
  font-size: 16px;
  color: #667eea;
  letter-spacing: 2px;
  pointer-events: none;
  z-index: 1;
}

.clear-btn {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  margin-right: 5px;
  transition: all 0.2s ease;
}

.clear-btn:active {
  background: rgba(0, 0, 0, 0.05);
}

.clear-icon {
  color: #ccc;
  font-size: 14px;
}

.error-text {
  color: #ff4757;
  font-size: 12px;
  margin-top: 5px;
  line-height: 1.4;
}

.help-text {
  color: #999;
  font-size: 12px;
  margin-top: 5px;
  line-height: 1.4;
}

.format-tip {
  color: #667eea;
  font-size: 11px;
  margin-top: 3px;
  line-height: 1.4;
  opacity: 0.8;
}
</style>

<template>
  <view class="register-container">
    <!-- 主要内容 -->
    <view class="main-content">
      <!-- 标题区域 -->
      <view class="title-section">
        <text class="main-title">注册</text>
        <text class="subtitle">输入数据时，请注意其准确性。</text>
      </view>

      <!-- 表单区域 -->
      <view class="form-section">
        <!-- 用户名输入 -->
        <FormInput
          label="用户名"
          v-model="formData.username"
          type="text"
          placeholder="请输入用户名（3-20位英文数字）"
          prefix-icon="👤"
          :disabled="loading"
          :error="errors.username"
          @input="handleUsernameInput"
        />

        <!-- 密码输入 -->
        <FormInput
          label="密码"
          v-model="formData.password"
          type="password"
          placeholder="请输入密码（至少8位，包含大小写字母和数字）"
          prefix-icon="🔒"
          :disabled="loading"
          :error="errors.password"
          @input="handlePasswordInput"
        />

        <!-- 确认密码输入 -->
        <FormInput
          label="确认密码"
          v-model="formData.confirmPassword"
          type="password"
          placeholder="请再次输入密码"
          prefix-icon="🔒"
          :disabled="loading"
          :error="errors.confirmPassword"
          @input="handleConfirmPasswordInput"
        />

        <!-- 激活码输入 -->
        <FormInput
          label="激活码"
          v-model="formData.activationCode"
          type="text"
          placeholder="请输入激活码"
          prefix-icon="🎫"
          :disabled="loading"
          :error="errors.activationCode"
          @input="handleActivationCodeInput"
        />

        <!-- 服务条款同意 -->
        <view class="terms-section">
          <view class="checkbox-wrapper" @tap="toggleTermsAgreement">
            <view class="checkbox" :class="{ 'checked': formData.agreeToTerms }">
              <text v-if="formData.agreeToTerms" class="check-icon">✓</text>
            </view>
            <view class="terms-text">
              <text class="terms-normal">我已阅读并同意 </text>
              <text class="terms-link" @tap.stop="viewTerms">服务条款</text>
            </view>
          </view>
          <view v-if="errors.agreeToTerms" class="error-text">
            {{ errors.agreeToTerms }}
          </view>
        </view>

        <!-- 错误提示 -->
        <view v-if="errors.general" class="error-message">
          {{ errors.general }}
        </view>

        <!-- 注册按钮 -->
        <FormButton
          text="注册"
          type="primary"
          size="large"
          block
          :loading="loading"
          :disabled="!canSubmit"
          loading-text="注册中..."
          @click="handleRegister"
        />

        <!-- 登录链接 -->
        <view class="login-section">
          <text class="login-text">您已经有账户了吗？ </text>
          <FormLink
            text="登录"
            type="primary"
            size="large"
            @click="goToLogin"
          />
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { createValidator, validators } from '@/utils/form-validator.js';
import { AuthHelper } from '@/utils/auth-helper.js';
import FormInput from '@/components/form/FormInput.vue';
import FormButton from '@/components/form/FormButton.vue';
import FormLink from '@/components/form/FormLink.vue';

export default {
  components: {
    FormInput,
    FormButton,
    FormLink
  },

  data() {
    return {
      formData: {
        username: '',
        password: '',
        confirmPassword: '',
        activationCode: '',
        agreeToTerms: false
      },
      loading: false,
      errors: {},
      validator: null
    };
  },

  created() {
    // 创建表单验证器
    this.validator = createValidator('register');
  },

  computed: {
    canSubmit() {
      return this.formData.username.trim() &&
             this.formData.password.trim() &&
             this.formData.confirmPassword.trim() &&
             this.formData.activationCode.trim() &&
             this.formData.agreeToTerms &&
             !this.loading;
    }
  },

  onLoad() {
    // 页面加载时的初始化
  },

  methods: {
    // 处理用户名输入
    handleUsernameInput(value) {
      this.formData.username = value;
      this.clearError('username');
      this.clearError('general');
    },

    // 处理密码输入
    handlePasswordInput(value) {
      this.formData.password = value;
      this.clearError('password');
      this.clearError('general');
      // 如果确认密码已输入，重新验证确认密码
      if (this.formData.confirmPassword) {
        this.validateConfirmPassword();
      }
    },

    // 处理确认密码输入
    handleConfirmPasswordInput(value) {
      this.formData.confirmPassword = value;
      this.clearError('confirmPassword');
      this.clearError('general');
      this.validateConfirmPassword();
    },

    // 处理激活码输入
    handleActivationCodeInput(value) {
      // 自动格式化激活码：移除连字符和空格，转为大写
      const cleanValue = value.replace(/[-\s]/g, '').toUpperCase();
      this.formData.activationCode = cleanValue;
      this.clearError('activationCode');
      this.clearError('general');
    },

    // 验证确认密码
    validateConfirmPassword() {
      if (this.formData.confirmPassword && this.formData.password !== this.formData.confirmPassword) {
        this.errors = { ...this.errors, confirmPassword: '两次输入的密码不一致' };
      } else {
        this.clearError('confirmPassword');
      }
    },

    // 切换服务条款同意状态
    toggleTermsAgreement() {
      this.formData.agreeToTerms = !this.formData.agreeToTerms;
      this.clearError('agreeToTerms');
    },

    // 查看服务条款
    viewTerms() {
      uni.showToast({
        title: '服务条款功能开发中',
        icon: 'none',
        duration: 2000
      });
    },

    // 表单验证
    validateForm() {
      const { isValid, errors } = this.validator.validate(this.formData);

      // 手动验证确认密码
      if (this.formData.confirmPassword && this.formData.password !== this.formData.confirmPassword) {
        errors.confirmPassword = '两次输入的密码不一致';
      }

      this.errors = errors;
      return isValid && !errors.confirmPassword;
    },

    // 清除错误
    clearError(field) {
      if (this.errors[field]) {
        const newErrors = { ...this.errors };
        delete newErrors[field];
        this.errors = newErrors;
      }
    },

    // 处理注册
    async handleRegister() {
      // 验证服务条款同意
      if (!this.formData.agreeToTerms) {
        this.errors = { ...this.errors, agreeToTerms: '请同意服务条款' };
        return;
      }

      // 表单验证（包括确认密码验证）
      if (!this.validateForm()) {
        return;
      }

      this.loading = true;
      this.errors = {};

      try {
        // 清理激活码格式（移除连字符和空格，转为大写）
        const cleanActivationCode = this.formData.activationCode.replace(/[-\s]/g, '').toUpperCase();

        const result = await AuthHelper.register({
          username: this.formData.username.trim(),
          email: `${this.formData.username.trim()}@temp.local`, // 临时邮箱格式
          password: this.formData.password.trim(),
          activation_code: cleanActivationCode
        });

        if (result.success) {
          // 注册成功，AuthHelper已经显示了成功提示，直接跳转
          setTimeout(() => {
            uni.navigateTo({
              url: '/pages/login/login'
            });
          }, 2000);
        } else {
          // AuthHelper已经显示了错误提示，这里只设置表单错误状态
          this.errors.general = result.error || '注册失败，请重试';
        }

      } catch (error) {
        // 这里通常不会执行，因为AuthHelper已经处理了所有错误
        console.error('注册失败:', error);
        this.errors.general = '注册失败，请重试';
      } finally {
        this.loading = false;
      }
    },

    // 跳转到登录页面
    goToLogin() {
      uni.navigateTo({
        url: '/pages/login/login'
      });
    }
  }
};
</script>

<style scoped>
.register-container {
  width: 100%;
  min-height: 100vh;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-radius: 80rpx;
}
/* 主要内容 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 60rpx;
}

/* 标题区域 */
.title-section {
  width: 100%;
  text-align: center;
  margin-top: 92rpx;
  margin-bottom: 42rpx;
}

.main-title {
  font-family: 'Samsung Sharp Sans', sans-serif;
  font-size: 60rpx;
  font-weight: bold;
  color: #F2282D;
  line-height: 80rpx;
  display: block;
  margin-bottom: 16rpx;
}

.subtitle {
  font-family: 'Samsung Sharp Sans', sans-serif;
  font-size: 30rpx;
  font-weight: 500;
  color: #8A8A8A;
  line-height: 44rpx;
  display: block;
}

/* 表单区域 */
.form-section {
  width: 100%;
  max-width: 670rpx;
}

/* 服务条款区域 */
.terms-section {
  margin: 32rpx 0;
}

.checkbox-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
  cursor: pointer;
}

.checkbox {
  width: 36rpx;
  height: 36rpx;
  border-radius: 8rpx;
  background: #D2D2D2;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
  margin-top: 6rpx;
}

.checkbox.checked {
  background: #F2282D;
}

.check-icon {
  color: #ffffff;
  font-size: 20rpx;
  font-weight: bold;
}

.terms-text {
  flex: 1;
  line-height: 48rpx;
}

.terms-normal {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-size: 24rpx;
  font-weight: 500;
  color: #000000;
}

.terms-link {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-size: 24rpx;
  font-weight: normal;
  color: #1676F3;
  text-decoration: underline;
  cursor: pointer;
}

/* 错误提示 */
.error-text {
  color: #F2282D;
  font-size: 14rpx;
  margin-top: 8rpx;
  text-align: center;
}

.error-message {
  background: #ffe6e6;
  color: #F2282D;
  padding: 12rpx;
  border-radius: 12rpx;
  font-size: 14rpx;
  margin-bottom: 20rpx;
  text-align: center;
}

/* 注册按钮区域 */
.form-section :deep(.form-button--large) {
  margin-top: 32rpx;
  margin-bottom: 28rpx;
}

/* 登录链接 */
.login-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4rpx;
  margin-top: 28rpx;
  margin-left: 20rpx;
}

.login-text {
  font-family: 'Poppins', sans-serif;
  font-size: 30rpx;
  font-weight: 500;
  color: #000000;
  letter-spacing: -0.3rpx;
  line-height: normal;
}
</style>

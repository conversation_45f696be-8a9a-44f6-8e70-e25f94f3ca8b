import { NextRequest, NextResponse } from 'next/server';

// 允许的源地址
const allowedOrigins = [
  'http://localhost:5173',  // UniApp H5开发环境
  'http://localhost:3000',  // Next.js开发环境
  'http://localhost:3333',  // 后端开发环境
  'https://your-domain.com' // 生产环境域名
];

// CORS配置选项
const corsOptions = {
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
  'Access-Control-Allow-Credentials': 'true',
  'Access-Control-Max-Age': '86400', // 24小时
};

export function middleware(request: NextRequest) {
  // 获取请求的源地址
  const origin = request.headers.get('origin') ?? '';
  const isAllowedOrigin = allowedOrigins.includes(origin);

  // 处理预检请求 (OPTIONS)
  const isPreflight = request.method === 'OPTIONS';

  if (isPreflight) {
    const preflightHeaders = {
      ...(isAllowedOrigin && { 'Access-Control-Allow-Origin': origin }),
      ...corsOptions,
    };
    return NextResponse.json({}, { headers: preflightHeaders });
  }

  // 处理普通请求
  const response = NextResponse.next();

  // 设置CORS headers
  if (isAllowedOrigin) {
    response.headers.set('Access-Control-Allow-Origin', origin);
  }

  Object.entries(corsOptions).forEach(([key, value]) => {
    response.headers.set(key, value);
  });

  return response;
}

// 配置中间件匹配路径
export const config = {
  matcher: [
    // 匹配所有API路由
    '/api/:path*',
    // 排除静态文件和Next.js内部路由
    '/((?!_next/static|_next/image|favicon.ico|sitemap.xml|robots.txt).*)',
  ],
};

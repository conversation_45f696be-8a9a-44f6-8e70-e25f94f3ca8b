import { NextRequest } from 'next/server';
import { runMigrations } from '@/lib/database';
import { 
  successResponse, 
  errorResponse, 
  withAdminAccess
} from '@/lib/api-utils';

// POST /api/admin/migrate - Run database migrations (admin only)
export async function POST(request: NextRequest) {
  return withAdminAccess()(request, async (req, user, session, clientInfo) => {
    try {
      await runMigrations();
      return successResponse(null, 'Database migrations completed successfully');
    } catch (error) {
      console.error('Migration error:', error);
      return errorResponse('Failed to run migrations', 500);
    }
  });
}

<template>
  <view class="login-container">
    <!-- 主要内容 -->
    <view class="main-content">
      <!-- 标题区域 -->
      <view class="title-section">
        <text class="main-title">Sign in</text>
        <text class="subtitle">When entering data, please pay attention to its accuracy.</text>
      </view>

      <!-- 表单区域 -->
      <view class="form-section">
        <!-- 邮箱输入 -->
        <FormInput
          label="Email"
          v-model="formData.emailOrUsername"
          type="text"
          placeholder="<EMAIL>"
          prefix-icon="📧"
          :disabled="loading"
          :error="errors.emailOrUsername"
          @input="handleEmailInput"
        />

        <!-- 密码输入 -->
        <FormInput
          label="Password"
          v-model="formData.password"
          type="password"
          placeholder="********"
          prefix-icon="🔒"
          :disabled="loading"
          :error="errors.password"
          @input="handlePasswordInput"
        />

        <!-- 忘记密码 -->
        <view class="forgot-password">
          <FormLink
            text="Forgot your password?"
            type="primary"
            size="medium"
            @click="handleForgotPassword"
          />
        </view>

        <!-- 错误提示 -->
        <view v-if="errors.general" class="error-message">
          {{ errors.general }}
        </view>

        <!-- 登录按钮 -->
        <FormButton
          text="Daxil ol"
          type="primary"
          size="large"
          block
          :loading="loading"
          :disabled="!canSubmit"
          loading-text="登录中..."
          @click="handleLogin"
        />

        <!-- 注册链接 -->
        <view class="register-section">
          <text class="register-text">Don't have an account?</text>
          <FormLink
            text="Sign up"
            type="primary"
            size="large"
            @click="goToRegister"
          />
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { createValidator } from '@/utils/form-validator.js';
import { AuthHelper } from '@/utils/auth-helper.js';
import FormInput from '@/components/form/FormInput.vue';
import FormButton from '@/components/form/FormButton.vue';
import FormLink from '@/components/form/FormLink.vue';

export default {
  components: {
    FormInput,
    FormButton,
    FormLink
  },
  data() {
    return {
      formData: {
        emailOrUsername: '',
        password: ''
      },
      showPassword: false,
      loading: false,
      errors: {},
      validator: null
    };
  },

  created() {
    // 创建表单验证器
    this.validator = createValidator('login');
  },

  computed: {
    canSubmit() {
      return this.formData.emailOrUsername.trim() && 
             this.formData.password.trim() && 
             !this.loading;
    }
  },

  onLoad() {
    // 页面加载时的初始化
  },

  methods: {
    // 切换密码显示
    togglePassword() {
      this.showPassword = !this.showPassword;
    },

    // 表单验证
    validateForm() {
      const { isValid, errors } = this.validator.validate(this.formData);
      this.errors = errors;
      return isValid;
    },

    // 验证单个字段
    validateField(field) {
      const error = this.validator.validateField(field, this.formData[field]);
      if (error) {
        this.$set(this.errors, field, error);
      } else {
        this.$delete(this.errors, field);
      }
      return !error;
    },

    // 处理邮箱输入
    handleEmailInput(value) {
      this.formData.emailOrUsername = value;
      this.clearError('emailOrUsername');
      this.clearError('general');
    },

    // 处理密码输入
    handlePasswordInput(value) {
      this.formData.password = value;
      this.clearError('password');
      this.clearError('general');
    },

    // 清除错误
    clearError(field) {
      if (this.errors[field]) {
        this.$delete(this.errors, field);
      }
    },

    // 处理登录
    async handleLogin() {
      if (!this.validateForm()) {
        return;
      }

      this.loading = true;
      this.errors = {};

      try {
        const result = await AuthHelper.login({
          emailOrUsername: this.formData.emailOrUsername.trim(),
          password: this.formData.password
        });

        if (result.success) {
          // 登录成功，跳转到首页
          setTimeout(() => {
            uni.reLaunch({
              url: '/pages/index/index'
            });
          }, 1000);
        } else {
          this.errors.general = result.error || '登录失败，请重试';
        }

      } catch (error) {
        console.error('登录失败:', error);
        this.errors.general = error.message || '登录失败，请重试';
      } finally {
        this.loading = false;
      }
    },

    // 跳转到注册页面
    goToRegister() {
      uni.navigateTo({
        url: '/pages/register/register'
      });
    },

    // 处理忘记密码
    handleForgotPassword() {
      uni.showToast({
        title: '忘记密码功能开发中',
        icon: 'none',
        duration: 2000
      });
    },

    // 清除错误
    clearError(field) {
      if (this.errors[field]) {
        this.$delete(this.errors, field);
      }
    }
  }
};
</script>

<style scoped>
.login-container {
  width: 100%;
  min-height: 100vh;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-radius: 80rpx;
}



/* 主要内容 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 60rpx;
}

/* 标题区域 */
.title-section {
  width: 100%;
  text-align: center;
  margin-top: 96rpx;
  margin-bottom: 106rpx;
}

.main-title {
  font-family: 'Samsung Sharp Sans', sans-serif;
  font-size: 64rpx;
  font-weight: bold;
  color: #F2282D;
  line-height: 80rpx;
  letter-spacing: -0.32rpx;
  display: block;
  margin-bottom: 8rpx;
}

.subtitle {
  font-family: 'Samsung Sharp Sans', sans-serif;
  font-size: 30rpx;
  font-weight: 500;
  color: #8A8A8A;
  line-height: 44rpx;
  display: block;
}

/* 表单区域 */
.form-section {
  width: 100%;
  max-width: 670rpx;
}

/* 忘记密码 */
.forgot-password {
  text-align: center;
  margin: 20rpx 0;
}

/* 错误提示 */
.error-text {
  color: #F2282D;
  font-size: 14rpx;
  margin-top: 8rpx;
  text-align: center;
}

.error-message {
  background: #ffe6e6;
  color: #F2282D;
  padding: 12rpx;
  border-radius: 12rpx;
  font-size: 14rpx;
  margin-bottom: 20rpx;
  text-align: center;
}

/* 登录按钮区域 */
.form-section :deep(.form-button--large) {
  margin-top: 80rpx;
  margin-bottom: 14rpx;
}

/* 注册链接 */
.register-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4rpx;
  margin-top: 14rpx;
}

.register-text {
  font-family: 'Samsung Sharp Sans', sans-serif;
  font-size: 28rpx;
  font-weight: 500;
  color: #000000;
  letter-spacing: 0.14rpx;
  line-height: 51.62rpx;
}
</style>

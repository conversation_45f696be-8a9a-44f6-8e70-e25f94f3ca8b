import App from './App'
import api from './utils/api.js'
import API from './api/index.js'
import { AuthHelper } from './utils/auth-helper.js'
import { AppInitializer } from './utils/app-init.js'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'

// 全局注册API工具
Vue.prototype.$api = api
Vue.prototype.$API = API
Vue.prototype.$auth = AuthHelper

// 全局混入认证检查
Vue.mixin({
  beforeCreate() {
    // 在页面创建前检查路由权限
    const currentPages = getCurrentPages();
    if (currentPages.length > 0) {
      const currentPage = currentPages[currentPages.length - 1];
      const pagePath = '/' + currentPage.route;

      // 检查页面访问权限
      AppInitializer.checkPageAccess(pagePath);
    }
  }
});

Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
export function createApp() {
  const app = createSSRApp(App)

  // 全局注册API工具
  app.config.globalProperties.$api = api
  app.config.globalProperties.$API = API

  return {
    app
  }
}
// #endif
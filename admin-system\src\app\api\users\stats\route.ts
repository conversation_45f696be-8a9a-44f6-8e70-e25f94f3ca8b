import { NextRequest } from 'next/server';
import { UserManager } from '@/lib/auth';
import { 
  successResponse, 
  errorResponse, 
  logAuditEvent,
  withAdminAccess
} from '@/lib/api-utils';

// GET /api/users/stats - Get user statistics (admin only)
export async function GET(request: NextRequest) {
  return withAdminAccess()(request, async (req, user, session, clientInfo) => {
    try {
      const stats = await UserManager.getUserStats();

      await logAuditEvent({
        userId: user.id,
        action: 'USER_STATS_VIEWED',
        resource: 'users',
        details: { stats },
      });

      return successResponse(stats, 'User statistics retrieved successfully');
    } catch (error) {
      console.error('Get user stats error:', error);
      return errorResponse('Failed to retrieve user statistics', 500);
    }
  });
}

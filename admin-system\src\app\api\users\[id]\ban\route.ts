import { NextRequest } from 'next/server';
import { UserManager } from '@/lib/auth';
import { 
  successResponse, 
  errorResponse, 
  validateRequest, 
  validators,
  logAuditEvent,
  withAdminAccess
} from '@/lib/api-utils';

// POST /api/users/[id]/ban - Ban user (admin only)
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return withAdminAccess()(request, async (req, user, session, clientInfo) => {
    try {
      const userId = parseInt(params.id);
      if (isNaN(userId)) {
        return errorResponse('Invalid user ID', 400);
      }

      // Don't allow banning yourself
      if (userId === user.id) {
        return errorResponse('Cannot ban yourself', 400);
      }

      const body = await request.json();
      
      // Validate request
      const { isValid, errors, data } = validateRequest<{
        reason: string;
        expiresAt?: string;
      }>(body, {
        reason: validators.required,
        expiresAt: (value) => {
          if (!value) return null;
          const date = new Date(value);
          if (isNaN(date.getTime())) {
            return 'Invalid date format';
          }
          if (date <= new Date()) {
            return 'Expiration date must be in the future';
          }
          return null;
        },
      });

      if (!isValid) {
        return errorResponse('Validation failed', 400, 'VALIDATION_ERROR');
      }

      // Check if target user exists
      const targetUser = await UserManager.getUserById(userId);
      if (!targetUser) {
        return errorResponse('User not found', 404);
      }

      // Don't allow banning admins (unless you're a super admin)
      if (targetUser.role === 'admin' && user.role !== 'admin') {
        return errorResponse('Cannot ban admin users', 403);
      }

      // Ban the user
      const expiresAt = data.expiresAt ? new Date(data.expiresAt) : undefined;
      await UserManager.banUser(userId, data.reason, user.id, expiresAt);

      await logAuditEvent({
        userId: user.id,
        action: 'USER_BANNED',
        resource: 'users',
        resourceId: userId.toString(),
        details: {
          targetUserId: userId,
          reason: data.reason,
          expiresAt: expiresAt?.toISOString(),
        },
      });

      return successResponse(null, 'User banned successfully');
    } catch (error) {
      console.error('Ban user error:', error);
      return errorResponse('Failed to ban user', 500);
    }
  });
}

/**
 * 客户端检测中间件
 * 用于区分不同的客户端类型（admin-system, uniapp等）
 */

import { NextRequest } from 'next/server';

// 客户端类型枚举
export enum ClientType {
  ADMIN_WEB = 'admin-web',
  UNIAPP = 'uniapp',
  MOBILE_APP = 'mobile-app',
  THIRD_PARTY = 'third-party',
  UNKNOWN = 'unknown'
}

// 客户端信息接口
export interface ClientInfo {
  type: ClientType;
  version?: string;
  platform?: string;
  userAgent: string;
  ipAddress: string;
}

// 客户端检测器
export class ClientDetector {
  /**
   * 检测客户端类型
   */
  static detectClientType(request: NextRequest): ClientType {
    const userAgent = request.headers.get('user-agent') || '';
    const clientType = request.headers.get('x-client-type');
    const clientVersion = request.headers.get('x-client-version');
    
    // 优先使用自定义header
    if (clientType) {
      switch (clientType.toLowerCase()) {
        case 'admin-web':
        case 'admin-system':
          return ClientType.ADMIN_WEB;
        case 'uniapp':
        case 'uni-app':
          return ClientType.UNIAPP;
        case 'mobile-app':
          return ClientType.MOBILE_APP;
        case 'third-party':
          return ClientType.THIRD_PARTY;
        default:
          break;
      }
    }

    // 基于User-Agent检测
    if (userAgent.includes('uni-app')) {
      return ClientType.UNIAPP;
    }
    
    if (userAgent.includes('AdminSystem')) {
      return ClientType.ADMIN_WEB;
    }
    
    // 检测移动端
    if (/Mobile|Android|iPhone|iPad/.test(userAgent)) {
      return ClientType.MOBILE_APP;
    }
    
    // 检测浏览器
    if (/Mozilla|Chrome|Safari|Firefox/.test(userAgent)) {
      return ClientType.ADMIN_WEB;
    }

    return ClientType.UNKNOWN;
  }

  /**
   * 获取客户端信息
   */
  static getClientInfo(request: NextRequest): ClientInfo {
    const userAgent = request.headers.get('user-agent') || '';
    const clientVersion = request.headers.get('x-client-version');
    const clientPlatform = request.headers.get('x-client-platform');
    
    return {
      type: this.detectClientType(request),
      version: clientVersion || undefined,
      platform: clientPlatform || this.detectPlatform(userAgent),
      userAgent,
      ipAddress: this.getClientIP(request),
    };
  }

  /**
   * 检测平台
   */
  private static detectPlatform(userAgent: string): string {
    if (/Windows/.test(userAgent)) return 'windows';
    if (/Mac/.test(userAgent)) return 'macos';
    if (/Linux/.test(userAgent)) return 'linux';
    if (/Android/.test(userAgent)) return 'android';
    if (/iPhone|iPad/.test(userAgent)) return 'ios';
    return 'unknown';
  }

  /**
   * 获取客户端IP地址
   */
  private static getClientIP(request: NextRequest): string {
    const forwarded = request.headers.get('x-forwarded-for');
    const realIP = request.headers.get('x-real-ip');
    
    if (forwarded) {
      return forwarded.split(',')[0].trim();
    }
    
    if (realIP) {
      return realIP;
    }
    
    return 'unknown';
  }

  /**
   * 检查是否为管理端客户端
   */
  static isAdminClient(clientType: ClientType): boolean {
    return clientType === ClientType.ADMIN_WEB;
  }

  /**
   * 检查是否为移动端客户端
   */
  static isMobileClient(clientType: ClientType): boolean {
    return [ClientType.UNIAPP, ClientType.MOBILE_APP].includes(clientType);
  }

  /**
   * 检查是否为可信客户端
   */
  static isTrustedClient(clientType: ClientType): boolean {
    return [ClientType.ADMIN_WEB, ClientType.UNIAPP, ClientType.MOBILE_APP].includes(clientType);
  }
}

// 客户端权限配置
export const CLIENT_PERMISSIONS = {
  [ClientType.ADMIN_WEB]: {
    canAccessAdminAPI: true,
    canAccessUserAPI: true,
    canManageUsers: true,
    canViewAnalytics: true,
    canExportData: true,
    rateLimit: {
      requests: 1000,
      window: 3600000, // 1小时
    },
  },
  [ClientType.UNIAPP]: {
    canAccessAdminAPI: false,
    canAccessUserAPI: true,
    canManageUsers: false,
    canViewAnalytics: false,
    canExportData: false,
    rateLimit: {
      requests: 500,
      window: 3600000, // 1小时
    },
  },
  [ClientType.MOBILE_APP]: {
    canAccessAdminAPI: false,
    canAccessUserAPI: true,
    canManageUsers: false,
    canViewAnalytics: false,
    canExportData: false,
    rateLimit: {
      requests: 300,
      window: 3600000, // 1小时
    },
  },
  [ClientType.THIRD_PARTY]: {
    canAccessAdminAPI: false,
    canAccessUserAPI: true,
    canManageUsers: false,
    canViewAnalytics: false,
    canExportData: false,
    rateLimit: {
      requests: 100,
      window: 3600000, // 1小时
    },
  },
  [ClientType.UNKNOWN]: {
    canAccessAdminAPI: false,
    canAccessUserAPI: false,
    canManageUsers: false,
    canViewAnalytics: false,
    canExportData: false,
    rateLimit: {
      requests: 50,
      window: 3600000, // 1小时
    },
  },
};

// 客户端权限检查器
export class ClientPermissionChecker {
  /**
   * 检查客户端是否有特定权限
   */
  static hasPermission(clientType: ClientType, permission: keyof typeof CLIENT_PERMISSIONS[ClientType.ADMIN_WEB]): boolean {
    const permissions = CLIENT_PERMISSIONS[clientType];
    return permissions ? permissions[permission] as boolean : false;
  }

  /**
   * 获取客户端限流配置
   */
  static getRateLimit(clientType: ClientType): { requests: number; window: number } {
    const permissions = CLIENT_PERMISSIONS[clientType];
    return permissions?.rateLimit || CLIENT_PERMISSIONS[ClientType.UNKNOWN].rateLimit;
  }

  /**
   * 检查是否可以访问管理API
   */
  static canAccessAdminAPI(clientType: ClientType): boolean {
    return this.hasPermission(clientType, 'canAccessAdminAPI');
  }

  /**
   * 检查是否可以访问用户API
   */
  static canAccessUserAPI(clientType: ClientType): boolean {
    return this.hasPermission(clientType, 'canAccessUserAPI');
  }

  /**
   * 检查是否可以管理用户
   */
  static canManageUsers(clientType: ClientType): boolean {
    return this.hasPermission(clientType, 'canManageUsers');
  }

  /**
   * 检查是否可以查看分析数据
   */
  static canViewAnalytics(clientType: ClientType): boolean {
    return this.hasPermission(clientType, 'canViewAnalytics');
  }

  /**
   * 检查是否可以导出数据
   */
  static canExportData(clientType: ClientType): boolean {
    return this.hasPermission(clientType, 'canExportData');
  }
}

// 客户端中间件
export function withClientDetection(
  handler: (request: NextRequest, clientInfo: ClientInfo) => Promise<Response>
) {
  return async (request: NextRequest): Promise<Response> => {
    const clientInfo = ClientDetector.getClientInfo(request);
    
    // 记录客户端信息（可选）
    console.log('Client detected:', {
      type: clientInfo.type,
      version: clientInfo.version,
      platform: clientInfo.platform,
      ip: clientInfo.ipAddress,
    });

    return handler(request, clientInfo);
  };
}

// 客户端权限中间件
export function withClientPermission(
  permission: keyof typeof CLIENT_PERMISSIONS[ClientType.ADMIN_WEB],
  handler: (request: NextRequest, clientInfo: ClientInfo) => Promise<Response>
) {
  return withClientDetection(async (request: NextRequest, clientInfo: ClientInfo): Promise<Response> => {
    if (!ClientPermissionChecker.hasPermission(clientInfo.type, permission)) {
      return new Response(
        JSON.stringify({
          success: false,
          error: '客户端权限不足',
          code: 'INSUFFICIENT_CLIENT_PERMISSION',
        }),
        {
          status: 403,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }

    return handler(request, clientInfo);
  });
}

export default ClientDetector;

import { NextRequest } from 'next/server';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, SessionManager, JWTUtils } from '@/lib/auth';
import { 
  successResponse, 
  errorResponse, 
  validateRequest, 
  validators,
  logAuditEvent,
  getClientIP,
  rateLimit
} from '@/lib/api-utils';

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const clientIP = getClientIP(request);
    if (!rateLimit(`login:${clientIP}`, 5, 300000)) { // 5 attempts per 5 minutes
      return errorResponse('登录尝试次数过多，请稍后再试', 429);
    }

    const body = await request.json();

    // Validate request
    const { isValid, errors, data } = validateRequest<{
      emailOrUsername: string;
      password: string;
    }>(body, {
      emailOrUsername: validators.required,
      password: validators.required,
    });

    if (!isValid) {
      return errorResponse('验证失败', 400, 'VALIDATION_ERROR');
    }

    // Authenticate user
    const user = await UserManager.authenticateUser(
      data.emailOrUsername,
      data.password
    );

    if (!user) {
      // Log failed login attempt
      await logAuditEvent({
        action: 'LOGIN_FAILED',
        details: {
          emailOrUsername: data.emailOrUsername,
          reason: 'Invalid credentials',
        },
        ipAddress: clientIP,
        userAgent: request.headers.get('user-agent') || undefined,
      });

      return errorResponse('用户名或密码错误', 401, 'INVALID_CREDENTIALS');
    }

    // Create session
    const sessionId = await SessionManager.createSession(user.id);

    // Generate JWT token
    const token = JWTUtils.sign({
      userId: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      sessionId,
    });

    // Log successful login
    await logAuditEvent({
      userId: user.id,
      action: 'LOGIN_SUCCESS',
      details: {
        sessionId,
      },
      ipAddress: clientIP,
      userAgent: request.headers.get('user-agent') || undefined,
    });

    return successResponse({
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        full_name: user.full_name,
        role: user.role,
        avatar_url: user.avatar_url,
        last_login: user.last_login,
      },
      token,
      sessionId,
    }, '登录成功');

  } catch (error) {
    console.error('Login error:', error);
    return errorResponse('服务器内部错误', 500);
  }
}

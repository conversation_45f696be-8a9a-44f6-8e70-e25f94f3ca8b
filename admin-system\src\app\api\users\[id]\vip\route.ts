import { NextRequest } from 'next/server';
import { UserManager } from '@/lib/auth';
import { 
  successResponse, 
  errorResponse, 
  validateRequest, 
  validators,
  logAuditEvent,
  withAdminAccess
} from '@/lib/api-utils';

// POST /api/users/[id]/vip - Set VIP level (admin only)
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return withAdminAccess()(request, async (req, user, session, clientInfo) => {
    try {
      const userId = parseInt(params.id);
      if (isNaN(userId)) {
        return errorResponse('Invalid user ID', 400);
      }

      const body = await request.json();
      
      // Validate request
      const { isValid, errors, data } = validateRequest<{
        level: 'none' | 'bronze' | 'silver' | 'gold' | 'platinum' | 'diamond';
        expiresAt?: string;
      }>(body, {
        level: (value) => {
          const validLevels = ['none', 'bronze', 'silver', 'gold', 'platinum', 'diamond'];
          if (!validLevels.includes(value)) {
            return 'Invalid VIP level';
          }
          return null;
        },
        expiresAt: (value) => {
          if (!value) return null;
          const date = new Date(value);
          if (isNaN(date.getTime())) {
            return 'Invalid date format';
          }
          if (date <= new Date()) {
            return 'Expiration date must be in the future';
          }
          return null;
        },
      });

      if (!isValid) {
        return errorResponse('Validation failed', 400, 'VALIDATION_ERROR');
      }

      // Check if target user exists
      const targetUser = await UserManager.getUserById(userId);
      if (!targetUser) {
        return errorResponse('User not found', 404);
      }

      // Set VIP level
      const expiresAt = data.expiresAt ? new Date(data.expiresAt) : undefined;
      await UserManager.setVipLevel(userId, data.level, expiresAt);

      await logAuditEvent({
        userId: user.id,
        action: 'USER_VIP_UPDATED',
        resource: 'users',
        resourceId: userId.toString(),
        details: {
          targetUserId: userId,
          oldLevel: targetUser.vip_level,
          newLevel: data.level,
          expiresAt: expiresAt?.toISOString(),
        },
      });

      return successResponse(null, 'VIP level updated successfully');
    } catch (error) {
      console.error('Set VIP level error:', error);
      return errorResponse('Failed to set VIP level', 500);
    }
  });
}

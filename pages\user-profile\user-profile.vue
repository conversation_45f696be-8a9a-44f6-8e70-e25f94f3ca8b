<template>
	<view class="profile-container">
		<!-- 用户头像 -->
		<view class="avatar-section">
			<image class="avatar" :src="getUserAvatar()" mode="aspectFit"></image>
		</view>

		<!-- 用户信息 -->
		<view class="user-info">
			<text class="user-name">{{ getUserDisplayName() }}</text>
			<text class="user-email">{{ getUserEmail() }}</text>
		</view>
		
		<!-- 服务列表 -->
		<view class="services-section">
			<text class="services-title">服务</text>
			
			<view class="service-item" @click="goToSupport">
				<view class="service-left">
					<view class="service-icon-bg">
						<image class="service-icon" src="/static/figma-assets/assets/bd1b79e4-4715-4004-a7b3-e2e389d920bb.png" mode="aspectFit"></image>
					</view>
					<text class="service-text">帮助中心</text>
				</view>
				<image class="arrow-icon" src="/static/figma-assets/assets/343f0e56-7402-48b6-b725-9d243ae7e65d.png" mode="aspectFit"></image>
			</view>

			<view class="service-item" @click="goToContact">
				<view class="service-left">
					<view class="service-icon-bg">
						<image class="service-icon" src="/static/figma-assets/assets/1072999d-28ae-4367-9d21-6ecebdd537dc.png" mode="aspectFit"></image>
					</view>
					<text class="service-text">联系我们</text>
				</view>
				<image class="arrow-icon" src="/static/figma-assets/assets/3b1af6c3-a271-40f3-b330-e508e470e046.png" mode="aspectFit"></image>
			</view>

			<view class="service-item" @click="goToRate">
				<view class="service-left">
					<view class="service-icon-bg">
						<image class="service-icon" src="/static/figma-assets/assets/fdd91889-f928-4ede-9944-bbbfbc51cddc.png" mode="aspectFit"></image>
					</view>
					<text class="service-text">评价应用</text>
				</view>
				<image class="arrow-icon" src="/static/figma-assets/assets/ecc262e8-2715-492c-b7df-bc16709c24cb.png" mode="aspectFit"></image>
			</view>

			<view class="service-item" @click="goToInvite">
				<view class="service-left">
					<view class="service-icon-bg">
						<image class="service-icon" src="/static/figma-assets/assets/2fb124dd-4e20-43b0-bfbf-53e827645b11.png" mode="aspectFit"></image>
					</view>
					<text class="service-text">邀请朋友</text>
				</view>
				<image class="arrow-icon" src="/static/figma-assets/assets/5dc35605-8194-416b-bfd2-7c701f40845e.png" mode="aspectFit"></image>
			</view>
		</view>
	</view>
</template>

<script>
import { AuthHelper } from '@/utils/auth-helper.js'
import { pageLifecycleMixin } from '@/utils/app-init.js'

export default {
	mixins: [pageLifecycleMixin],
	data() {
		return {
			userInfo: null,
			loading: true,
			requireAuth: true // 标记此页面需要登录
		}
	},
	async onLoad() {
		await this.checkAuthAndLoadData();
	},
	onShow() {
		// 每次显示页面时检查登录状态
		if (!AuthHelper.isLoggedIn()) {
			AuthHelper.redirectToLogin();
			return;
		}
		// 刷新用户信息
		this.loadUserInfo();
	},
	methods: {
		// 检查登录状态并加载数据
		async checkAuthAndLoadData() {
			if (!AuthHelper.isLoggedIn()) {
				AuthHelper.redirectToLogin();
				return;
			}

			await this.loadUserInfo();
		},

		// 加载用户信息
		async loadUserInfo() {
			try {
				this.loading = true;
				const userInfo = AuthHelper.getCurrentUser();

				if (userInfo) {
					this.userInfo = userInfo;
				} else {
					// 如果本地没有用户信息，尝试从服务器获取
					const result = await AuthHelper.refreshUserInfo();
					if (result.success) {
						this.userInfo = result.user;
					} else {
						// 获取失败，可能token已过期
						AuthHelper.redirectToLogin();
						return;
					}
				}
			} catch (error) {
				console.error('加载用户信息失败:', error);
				AuthHelper.redirectToLogin();
			} finally {
				this.loading = false;
			}
		},

		// 获取用户显示名称
		getUserDisplayName() {
			if (!this.userInfo) return '未登录';
			return this.userInfo.full_name || this.userInfo.username || '用户';
		},

		// 获取用户邮箱
		getUserEmail() {
			if (!this.userInfo) return '';
			return this.userInfo.email || '';
		},

		// 获取用户头像
		getUserAvatar() {
			if (!this.userInfo || !this.userInfo.avatar_url) {
				return '/static/figma-assets/assets/b0b4af09-8098-4d18-aed3-71537580e1af.png';
			}
			return this.userInfo.avatar_url;
		},

		goToSupport() {
			console.log('前往帮助中心');
		},
		goToContact() {
			console.log('前往联系我们');
		},
		goToRate() {
			console.log('前往评价应用');
		},
		goToInvite() {
			console.log('前往邀请朋友');
		}
	}
}
</script>

<style scoped>
.profile-container {
	width: 100%;
	min-height: 100vh;
	background-color: #222222;
	display: flex;
	flex-direction: column;
	overflow-x: hidden;
}

/* 用户头像 */
.avatar-section {
	width: 100%;
	display: flex;
	justify-content: center;
	margin-top: 100rpx;
	/* 确保在微信小程序中不与状态栏和胶囊按钮重叠 */
	margin-top: calc(100rpx + env(safe-area-inset-top));
	/* 为微信小程序添加额外的顶部间距 */
	/* #ifdef MP-WEIXIN */
	margin-top: calc(140rpx + env(safe-area-inset-top));
	/* #endif */
}

.avatar {
	width: 136rpx;
	height: 136rpx;
	border-radius: 50%;
}

/* 用户信息 */
.user-info {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-top: 50rpx;
	padding: 0 124rpx;
	box-sizing: border-box;
}

.user-name {
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 32rpx;
	font-weight: bold;
	color: #FFFFFF;
	text-align: center;
	letter-spacing: 0.16rpx;
	margin-bottom: 6rpx;
}

.user-email {
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 26rpx;
	font-weight: normal;
	color: #FFFFFF;
	text-align: center;
	letter-spacing: 0.14rpx;
}

/* 服务列表 */
.services-section {
	width: 720rpx;
	margin: 84rpx auto 0;
	padding: 30rpx;
	background-color: rgba(255, 255, 255, 0.29);
	border-radius: 30rpx;
	box-sizing: border-box;
}

.services-title {
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 30rpx;
	font-weight: bold;
	color: #FFFFFF;
	letter-spacing: 0.16rpx;
	display: block;
	margin-bottom: 36rpx;
}

.service-item {
	width: 100%;
	height: 84rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 36rpx;
}

.service-item:last-child {
	margin-bottom: 0;
}

.service-left {
	display: flex;
	align-items: center;
}

.service-icon-bg {
	width: 84rpx;
	height: 84rpx;
	border-radius: 20rpx;
	background-color: rgba(255, 255, 255, 0.26);
	display: flex;
	justify-content: center;
	align-items: center;
	margin-right: 42rpx;
}

.service-icon {
	width: 44rpx;
	height: 44rpx;
}

.service-text {
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 30rpx;
	font-weight: 500;
	color: #FFFFFF;
	letter-spacing: 0.16rpx;
}

.arrow-icon {
	width: 84rpx;
	height: 84rpx;
	border-radius: 20rpx;
}
</style>
